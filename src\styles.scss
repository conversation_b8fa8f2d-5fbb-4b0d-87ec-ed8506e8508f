// Custom Theming for Angular Material
// For more information: https://material.angular.dev/guide/theming
@use '@angular/material' as mat;

html {
  @include mat.theme((color: (theme-type: light,
        primary: mat.$azure-palette,
        tertiary: mat.$blue-palette,
      ),
      typography: Roboto,
      density: 0,
    ));
}

/* You can add global styles to this file, and also import other style files */

html,
body {
  height: 100%;
}

body {
  margin: 0;
  font-family: Roboto, "Helvetica Neue", sans-serif;
  overflow-x: hidden; // Prevent horizontal scroll
}

// Ensure smooth scrolling and prevent layout shifts
* {
  box-sizing: border-box;
}

// Prevent any potential layout shifts from the fixed navbar
html {
  scroll-padding-top: 64px;
}

// Global Colors
:root {
  --absa-red: #911D2F; // icons and some buttons will have this color
  --backgound-white: #fff;
  --quick-actions-bg: #D9D9D9;

}

// Custom Snackbar Styles
.success-snackbar {
  background-color: #4caf50 !important;
  color: white !important;

  .mat-mdc-snack-bar-label {
    color: white !important;
  }

  .mat-mdc-button {
    color: white !important;
  }
}